<?php
/**
 * Minimal index file for debugging
 */

echo "Hello World!";

// Try to load WordPress
if (file_exists('../../../wp-load.php')) {
    require_once('../../../wp-load.php');
    echo "<br>WordPress loaded!";
    
    // Check if Timber is available
    if (class_exists('Timber\Timber')) {
        echo "<br>Timber is available!";
        
        // Try to get site info
        try {
            $site = new Timber\Site();
            echo "<br>Site: " . $site->name;
        } catch (Exception $e) {
            echo "<br>Error: " . $e->getMessage();
        }
    } else {
        echo "<br>Timber is not available!";
    }
} else {
    echo "<br>Could not load WordPress!";
}