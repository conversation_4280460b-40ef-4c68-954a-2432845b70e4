<?php
/**
 * Theme functions and definitions
 *
 * @package Themo
 * @since 1.0.0
 */

declare(strict_types=1);

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Add debugging
error_log('Themo theme functions.php loaded');

// Load composer dependencies
if (file_exists(__DIR__ . '/vendor/autoload.php')) {
    require_once __DIR__ . '/vendor/autoload.php';
    error_log('Composer autoload loaded');
} else {
    error_log('Composer autoload NOT found');
}

// Initialize theme
if (!function_exists('themo_init')) {
    /**
     * Initialize the theme.
     *
     * @return void
     */
    function themo_init(): void
    {
        error_log('Themo theme initialization started');
        
        $classes = [
            Themo\Setup\Theme_Setup::class,
            Themo\Assets\Asset_Manager::class,
            Themo\Setup\Security::class,
            Themo\Setup\Performance::class,
            Themo\Setup\Timber_Context::class,
            Themo\WooCommerce\WooCommerce_Integration::class,
        ];

        foreach ($classes as $class) {
            if (class_exists($class)) {
                error_log('Initializing class: ' . $class);
                $instance = new $class();
                if (method_exists($instance, 'init')) {
                    $instance->init();
                }
            } else {
                error_log('Class not found: ' . $class);
            }
        }
        
        error_log('Themo theme initialization completed');
    }
}
add_action('after_setup_theme', 'themo_init');