/*
Theme Name:     Themo WP
Theme URI:      https://example.com/themes/themo-wp
Description:    A modern WordPress boilerplate theme built with <PERSON><PERSON> and Twig.
Version:        1.0.0
Author:         Your Name
Requires at least: 6.0
Tested up to:   6.5
Requires PHP:   8.0
License:        GPL-2.0-or-later
License URI:    https://www.gnu.org/licenses/gpl-2.0.html
Text Domain:    themo-wp
Tags:           timber, twig, boilerplate, modern, responsive
*/

/*--------------------------------------------------------------
>>> TABLE OF CONTENTS:
----------------------------------------------------------------
# Generic
# Typography
# Elements
# Forms
# Navigation
# Accessibility
# Alignments
# Clearings
# Widgets
# Content
# Media
# Site
# Welcome Page
--------------------------------------------------------------*/

/*--------------------------------------------------------------
# Generic
--------------------------------------------------------------*/
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
h1, h2, h3, h4, h5, h6 {
    margin-top: 0;
    margin-bottom: 0.5em;
    font-weight: 600;
    line-height: 1.2;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.75rem;
}

h4 {
    font-size: 1.5rem;
}

p {
    margin-top: 0;
    margin-bottom: 1em;
}

a {
    color: #0073aa;
    text-decoration: none;
}

a:hover,
a:focus {
    text-decoration: underline;
}

/*--------------------------------------------------------------
# Elements
--------------------------------------------------------------*/
button,
.button {
    display: inline-block;
    padding: 0.75em 1.5em;
    border: 1px solid #0073aa;
    border-radius: 4px;
    background-color: #0073aa;
    color: #fff;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

button:hover,
button:focus,
.button:hover,
.button:focus {
    background-color: #005a87;
    border-color: #005a87;
    text-decoration: none;
    outline: 2px solid #005a87;
    outline-offset: 2px;
}

.button.secondary {
    background-color: transparent;
    color: #0073aa;
}

.button.secondary:hover,
.button.secondary:focus {
    background-color: rgba(0, 115, 170, 0.1);
    color: #005a87;
}

.button.large {
    padding: 1em 2em;
    font-size: 1.125rem;
}

/*--------------------------------------------------------------
# Forms
--------------------------------------------------------------*/
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
textarea,
select {
    padding: 0.5em;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
}

/*--------------------------------------------------------------
# Navigation
--------------------------------------------------------------*/
.main-navigation {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1em 0;
}

.menu-toggle {
    display: none;
}

.menu {
    display: flex;
    list-style: none;
}

.menu-item {
    position: relative;
    margin-left: 1.5em;
}

.menu-item:first-child {
    margin-left: 0;
}

.menu-item a {
    display: block;
    padding: 0.5em 0;
    color: #333;
    text-decoration: none;
}

.menu-item a:hover,
.menu-item a:focus {
    color: #0073aa;
}

.menu-item.current-menu-item > a {
    color: #0073aa;
    font-weight: 600;
}

.sub-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 100;
    display: none;
    min-width: 200px;
    margin: 0;
    padding: 0.5em 0;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    list-style: none;
}

.menu-item:hover > .sub-menu,
.menu-item:focus-within > .sub-menu {
    display: block;
}

.sub-menu .menu-item {
    margin: 0;
}

.sub-menu .menu-item a {
    padding: 0.5em 1em;
}

/*--------------------------------------------------------------
# Accessibility
--------------------------------------------------------------*/
.screen-reader-text {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

.screen-reader-text:focus {
    position: static;
    width: auto;
    height: auto;
    margin: 0;
    overflow: visible;
    clip: auto;
}

/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/
.alignleft {
    float: left;
    margin-right: 1.5em;
}

.alignright {
    float: right;
    margin-left: 1.5em;
}

.aligncenter {
    clear: both;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

/*--------------------------------------------------------------
# Clearings
--------------------------------------------------------------*/
.clear:before,
.clear:after,
.entry-content:before,
.entry-content:after,
.comment-content:before,
.comment-content:after,
.site-header:before,
.site-header:after,
.site-content:before,
.site-content:after,
.site-footer:before,
.site-footer:after {
    content: "";
    display: table;
    table-layout: fixed;
}

.clear:after,
.entry-content:after,
.comment-content:after,
.site-header:after,
.site-content:after,
.site-footer:after {
    clear: both;
}

/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
.widget {
    margin-bottom: 2em;
}

.widget-title {
    margin-top: 0;
}

/*--------------------------------------------------------------
# Content
--------------------------------------------------------------*/
.site {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5em;
}

.site-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1em 0;
    border-bottom: 1px solid #eee;
}

.site-branding {
    flex: 1;
}

.site-title {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    line-height: 1.2;
}

.site-title a {
    color: #333;
    text-decoration: none;
}

.site-title a:hover,
.site-title a:focus {
    color: #0073aa;
}

.site-description {
    margin: 0.25em 0 0;
    font-size: 0.875rem;
    color: #666;
}

.site-content {
    padding: 2em 0;
}

/*--------------------------------------------------------------
# Media
--------------------------------------------------------------*/
.custom-logo {
    max-height: 60px;
    width: auto;
}

img {
    max-width: 100%;
    height: auto;
}

/*--------------------------------------------------------------
# Site Footer
--------------------------------------------------------------*/
.site-footer {
    padding: 2em 0;
    margin-top: 2em;
    border-top: 1px solid #eee;
    background-color: #f9f9f9;
}

.footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
}

.footer-copyright {
    flex: 1;
    margin-bottom: 1em;
}

.footer-menu {
    display: flex;
    list-style: none;
}

.footer-menu .menu-item {
    margin-left: 1.5em;
}

.footer-menu .menu-item:first-child {
    margin-left: 0;
}

.footer-menu .menu-item a {
    color: #666;
    text-decoration: none;
}

.footer-menu .menu-item a:hover,
.footer-menu .menu-item a:focus {
    color: #0073aa;
}

/*--------------------------------------------------------------
# Welcome Page
--------------------------------------------------------------*/
.welcome-container {
    max-width: 1200px;
    margin: 0 auto;
}

.welcome-hero {
    text-align: center;
    padding: 4em 1em;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4edf9 100%);
    border-radius: 8px;
    margin-bottom: 3em;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 0.5em;
    color: #1a1a1a;
}

.hero-description {
    font-size: 1.25rem;
    margin-bottom: 2em;
    color: #666;
}

.hero-actions {
    display: flex;
    justify-content: center;
    gap: 1em;
    flex-wrap: wrap;
}

.features-section {
    padding: 3em 1em;
    margin-bottom: 3em;
}

.section-header {
    text-align: center;
    margin-bottom: 3em;
}

.section-title {
    font-size: 2.5rem;
    margin-bottom: 0.5em;
    color: #1a1a1a;
}

.section-description {
    font-size: 1.125rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2em;
}

.feature-card {
    text-align: center;
    padding: 2em;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.feature-icon {
    margin-bottom: 1.5em;
    color: #0073aa;
}

.feature-title {
    margin-bottom: 1em;
    font-size: 1.5rem;
}

.feature-description {
    color: #666;
    line-height: 1.6;
}

.cta-section {
    padding: 4em 1em;
    text-align: center;
    background: linear-gradient(135deg, #0073aa 0%, #005a87 100%);
    color: #fff;
    border-radius: 8px;
    margin-bottom: 3em;
}

.cta-title {
    font-size: 2.5rem;
    margin-bottom: 0.5em;
    color: #fff;
}

.cta-description {
    font-size: 1.25rem;
    margin-bottom: 2em;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-section .button {
    background-color: #fff;
    color: #0073aa;
    border-color: #fff;
}

.cta-section .button:hover,
.cta-section .button:focus {
    background-color: rgba(255, 255, 255, 0.9);
    color: #005a87;
}

/*--------------------------------------------------------------
# Responsive
--------------------------------------------------------------*/
@media (max-width: 768px) {
    .site-header {
        flex-direction: column;
        text-align: center;
    }
    
    .main-navigation {
        width: 100%;
        justify-content: center;
        margin-top: 1em;
    }
    
    .menu {
        justify-content: center;
    }
    
    .menu-item {
        margin: 0 0.75em;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .hero-description {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .footer-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-copyright {
        margin-bottom: 1em;
    }
    
    .footer-menu {
        justify-content: center;
    }
    
    .footer-menu .menu-item {
        margin: 0 0.75em;
    }
    
    .cta-title {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .site {
        padding: 0 1em;
    }
    
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .hero-actions .button {
        width: 100%;
        max-width: 250px;
    }
    
    .features-grid {
        grid-template-columns: 1fr;
    }
}