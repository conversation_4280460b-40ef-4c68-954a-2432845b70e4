# Themo WP Theme

A professional WordPress boilerplate theme built with <PERSON><PERSON> and <PERSON><PERSON>.

## Features
- Modern PHP with PSR-4 autoloading
- <PERSON><PERSON> and Twig templating
- Responsive design
- Performance optimized
- Security focused
- Developer friendly

## Setup Instructions
1. Activate the theme in WordPress
2. Create a primary menu and assign it to the "Primary Menu" location
3. Create a footer menu and assign it to the "Footer Menu" location
4. Set a static front page to see the welcome template

## Template Structure
- `front-page.php` - Welcome page template
- `index.php` - Blog index template
- `single.php` - Single post template
- `page.php` - Page template
- `archive.php` - Archive template
- `search.php` - Search results template
- `404.php` - 404 error template

## Customization
- Add your own styles in `assets/scss/`
- Add JavaScript in `assets/js/`
- Modify templates in `templates/` and `template-parts/`
- Extend functionality in `inc/classes/`