<?php
/**
 * Very simple test index file
 */

echo "<h1>Basic PHP is working!</h1>";

// Try to load WordPress
if (file_exists(__DIR__ . '/../../../wp-load.php')) {
    echo "<p>Found wp-load.php</p>";
    
    // Load WordPress
    require_once(__DIR__ . '/../../../wp-load.php');
    
    echo "<p>WordPress loaded successfully!</p>";
    
    // Check if we're in the theme context
    if (function_exists('get_bloginfo')) {
        echo "<p>Site name: " . get_bloginfo('name') . "</p>";
    }
    
    // Check if Timber is available
    if (class_exists('Timber\Timber')) {
        echo "<p>Timber is available!</p>";
        
        // Try to create a basic Timber context
        try {
            use Timber\Timber;
            use Timber\Site;
            
            $site = new Site();
            echo "<p>Timber Site object created: " . $site->name . "</p>";
            
        } catch (Exception $e) {
            echo "<p>Error with <PERSON><PERSON>: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>Timber is NOT available</p>";
    }
    
} else {
    echo "<p>Could not find wp-load.php</p>";
}

echo "<p>End of test</p>";