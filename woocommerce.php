<?php
/**
 * The template for displaying all WooCommerce pages
 *
 * @package Themo
 */

use Timber\Timber;
use Timber\Post;

if (!class_exists('WooCommerce')) {
    return;
}

$context = Timber::context();

if (is_singular('product')) {
    $context['post'] = Timber::get_post();
    $product = wc_get_product($context['post']->ID);
    $context['product'] = $product;
    Timber::render('woocommerce/single-product.twig', $context);
} elseif (is_post_type_archive('product') || is_tax(get_object_taxonomies('product'))) {
    $context['posts'] = Timber::get_posts();
    $context['title'] = get_the_archive_title();
    Timber::render('woocommerce/archive-product.twig', $context);
} else {
    $context['post'] = new Post();
    Timber::render('page.twig', $context);
}
