{"name": "themo-wp", "version": "1.0.0", "main": "vite.config.js", "directories": {"doc": "docs", "test": "tests"}, "scripts": {"dev": "vite", "build": "vite build", "build:production": "NODE_ENV=production vite build", "watch": "vite build --watch", "zip": "node tools/build/create-zip.js"}, "repository": {"type": "git", "url": "git+https://github.com/Starthinc/themo-wp.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Starthinc/themo-wp/issues"}, "homepage": "https://github.com/Starthinc/themo-wp#readme", "description": "", "dependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "vite": "^7.0.0"}, "devDependencies": {"sass-embedded": "^1.90.0"}}