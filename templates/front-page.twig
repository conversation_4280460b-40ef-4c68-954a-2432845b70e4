{% extends "layouts/app.twig" %}

{% block content %}
<div class="welcome-container">
    <h1>Front Page Template Loaded</h1>
    
    {% if site %}
        <p>Site name: {{ site.name }}</p>
    {% else %}
        <p>Site object not available</p>
    {% endif %}
    
    {% if menu %}
        <p>Primary menu found with {{ menu.items|length }} items</p>
    {% else %}
        <p>Primary menu not available</p>
    {% endif %}
    
    <section class="welcome-hero">
        <div class="hero-content">
            <h1 class="hero-title">Welcome to Themo WP</h1>
            <p class="hero-description">A modern WordPress boilerplate built with <PERSON><PERSON> and <PERSON>wig</p>
            <div class="hero-actions">
                <a href="#features" class="button primary">Explore Features</a>
                <a href="https://github.com" target="_blank" class="button secondary">View on GitHub</a>
            </div>
        </div>
    </section>

    <section id="features" class="features-section">
        <div class="section-header">
            <h2 class="section-title">Professional Features</h2>
            <p class="section-description">Built with modern development practices and performance in mind</p>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                        <path d="M2 17l10 5 10-5"></path>
                        <path d="M2 12l10 5 10-5"></path>
                    </svg>
                </div>
                <h3 class="feature-title">Timber & Twig</h3>
                <p class="feature-description">Modern templating with Timber and Twig for clean, maintainable code.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
                    </svg>
                </div>
                <h3 class="feature-title">Performance Focused</h3>
                <p class="feature-description">Optimized for speed and efficiency with best practices built-in.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                        <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                    </svg>
                </div>
                <h3 class="feature-title">Developer Friendly</h3>
                <p class="feature-description">Clean code structure with PSR-4 autoloading and modern PHP.</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                    </svg>
                </div>
                <h3 class="feature-title">Secure by Default</h3>
                <p class="feature-description">Security best practices implemented from the ground up.</p>
            </div>
        </div>
    </section>

    <section class="cta-section">
        <div class="cta-content">
            <h2 class="cta-title">Ready to Build Something Amazing?</h2>
            <p class="cta-description">Start creating your next WordPress project with Themo WP today.</p>
            <a href="https://developer.wordpress.org/themes/" target="_blank" class="button primary large">Get Started</a>
        </div>
    </section>
</div>
{% endblock %}