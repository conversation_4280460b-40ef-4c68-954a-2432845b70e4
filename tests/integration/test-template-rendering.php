<?php
/**
 * Test Template Rendering
 *
 * @package Themo\Tests
 */

use Timber\Timber;

class Test_Template_Rendering extends WP_UnitTestCase {
    
    public function test_single_post_renders(): void {
        $post_id = $this->factory()->post->create();
        $this->go_to(get_permalink($post_id));
        
        $context = Timber::context();
        $output = Timber::compile('single.twig', $context);
        
        $this->assertStringContainsString('<article', $output);
    }
}
