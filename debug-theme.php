<?php
/**
 * Debug script to check what's happening in our theme
 */

// Load WordPress
echo "Loading WordPress...\n";
require_once('../../../wp-load.php');

echo "WordPress loaded\n";

// Check if <PERSON>ber is available
echo "Checking if <PERSON><PERSON> is available...\n";
if (class_exists('Timber\\Timber')) {
    echo "Timber is available\n";
} else {
    echo "Timber is NOT available\n";
    exit;
}

// Import Timber classes
use Timber\Timber;
use Timber\Site;
use Timber\Menu;

// Try to create a site object
echo "Creating site object...\n";
try {
    $site = new Site();
    echo "Site object created: " . $site->name . "\n";
} catch (Exception $e) {
    echo "Error creating site object: " . $e->getMessage() . "\n";
    exit;
}

// Try to get a menu
echo "Getting primary menu...\n";
try {
    $menu = Timber::get_menu('primary');
    if ($menu) {
        echo "Primary menu found with " . count($menu->items) . " items\n";
    } else {
        echo "No primary menu found\n";
    }
} catch (Exception $e) {
    echo "Error getting primary menu: " . $e->getMessage() . "\n";
}

// Try to render a simple template
echo "Rendering simple template...\n";
try {
    $context = [
        'site' => $site,
        'menu' => $menu
    ];
    
    $output = Timber::compile_string('Site: {{ site.name }}', $context);
    echo "Template rendered: " . $output . "\n";
} catch (Exception $e) {
    echo "Error rendering template: " . $e->getMessage() . "\n";
}

echo "Debug script completed\n";