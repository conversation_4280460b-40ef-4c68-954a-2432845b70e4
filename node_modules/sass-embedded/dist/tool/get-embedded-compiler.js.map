{"version": 3, "file": "get-embedded-compiler.js", "sourceRoot": "", "sources": ["../../tool/get-embedded-compiler.ts"], "names": [], "mappings": ";AAAA,uEAAuE;AACvE,gEAAgE;AAChE,uCAAuC;;AAkBvC,kDA+CC;AA/DD,2BAAkC;AAClC,0BAA0B;AAC1B,iCAAiC;AAEjC,gEAA0D;AAC1D,iCAAiC;AAEjC;;;;;;;;GAQG;AACI,KAAK,UAAU,mBAAmB,CACvC,OAQK;IAEL,MAAM,IAAI,GAAG,WAAW,CAAC;IAEzB,IAAI,MAAc,CAAC;IACnB,IAAI,OAAO,KAAK,SAAS,IAAI,MAAM,IAAI,OAAO,EAAE,CAAC;QAC/C,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IACxB,CAAC;SAAM,CAAC;QACN,KAAK,CAAC,SAAS,CAAC;YACd,IAAI;YACJ,OAAO,EAAE,OAAO;YAChB,GAAG,EAAE,OAAO,EAAE,GAAG,IAAI,MAAM;SAC5B,CAAC,CAAC;QACH,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,6EAA6E;IAC7E,2EAA2E;IAC3E,qDAAqD;IACrD,MAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;IAC/C,MAAM,kBAAkB,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC,CAAC;IACvE,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,UAAU,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC,EAAE,CAAC;QAClE,MAAM,KAAK,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;QACzC,MAAM,KAAK,CAAC,IAAI,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;IACvD,CAAC;IAED,MAAM,EAAE,GAAG,OAAO,EAAE,EAAE,IAAI,KAAK,CAAC;IAChC,qBAAqB,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IAElC,MAAM,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;IACpD,MAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,gCAAc,CAAC,CAAC,CAAC;IACzE,IAAI,EAAE,EAAE,CAAC;QACP,MAAM,aAAE,CAAC,EAAE,CAAC,cAAc,EAAE,EAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;QAC5D,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,YAAY,CAAC,CAAC;IAC9D,CAAC;SAAM,CAAC;QACN,MAAM,aAAE,CAAC,EAAE,CAAC,YAAY,EAAE,EAAC,KAAK,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAC,CAAC,CAAC;QAC1D,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1E,CAAC;AACH,CAAC;AAED,0EAA0E;AAC1E,SAAS,qBAAqB,CAAC,QAAgB,EAAE,EAAW;IAC1D,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;IACrD,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE;QAC7B,GAAG,EAAE,QAAQ;QACb,MAAM,EAAE,IAAI;KACb,CAAC,CAAC;IAEH,IAAI,EAAE,EAAE,CAAC;QACP,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE;YACxB,GAAG,EAAE,QAAQ;YACb,MAAM,EAAE,IAAI;SACb,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,uCAAuC,EAAE;YAClD,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,EAAC,GAAG,OAAO,CAAC,GAAG,EAAE,oBAAoB,EAAE,OAAO,EAAC;SACrD,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAClD,KAAK,CAAC,IAAI,CAAC,8CAA8C,EAAE;YACzD,GAAG,EAAE,QAAQ;YACb,GAAG,EAAE,EAAC,GAAG,OAAO,CAAC,GAAG,EAAE,oBAAoB,EAAE,OAAO,EAAC;SACrD,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}