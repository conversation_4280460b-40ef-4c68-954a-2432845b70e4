{"version": 3, "file": "prepare-optional-release.js", "sourceRoot": "", "sources": ["../../tool/prepare-optional-release.ts"], "names": [], "mappings": ";;AAgCA,gEAcC;AAKD,gDAaC;AAhED,0CAA2C;AAC3C,2BAAkC;AAClC,0BAA0B;AAC1B,6BAA0C;AAC1C,iCAA0B;AAE1B,uCAAuC;AACvC,iCAAiC;AAUjC,MAAM,IAAI,GAAG,IAAA,eAAK,EAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;KACtC,MAAM,CAAC,SAAS,EAAE;IACjB,IAAI,EAAE,QAAQ;IACd,WAAW,EACT,2EAA2E;IAC7E,YAAY,EAAE,IAAI;IAClB,OAAO,EAAE,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC,GAAG,CAChD,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,CACxC;CACF,CAAC;KACD,SAAS,EAAE,CAAC;AAEf,+EAA+E;AAC/E,6EAA6E;AAC7E,sBAAsB;AACtB,SAAgB,0BAA0B,CAAC,QAAgB;IACzD,QAAQ,QAAQ,EAAE,CAAC;QACjB,KAAK,SAAS;YACZ,OAAO,SAAS,CAAC;QACnB,KAAK,OAAO,CAAC;QACb,KAAK,YAAY;YACf,OAAO,OAAO,CAAC;QACjB,KAAK,QAAQ;YACX,OAAO,OAAO,CAAC;QACjB,KAAK,OAAO;YACV,OAAO,SAAS,CAAC;QACnB;YACE,MAAM,KAAK,CAAC,YAAY,QAAQ,oBAAoB,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC;AAED,+EAA+E;AAC/E,8EAA8E;AAC9E,iBAAiB;AACjB,SAAgB,kBAAkB,CAAC,IAAY;IAC7C,QAAQ,IAAI,EAAE,CAAC;QACb,KAAK,KAAK;YACR,OAAO,KAAK,CAAC;QACf,KAAK,KAAK;YACR,OAAO,KAAK,CAAC;QACf,KAAK,OAAO;YACV,OAAO,OAAO,CAAC;QACjB,KAAK,SAAS;YACZ,OAAO,SAAS,CAAC;QACnB;YACE,MAAM,KAAK,CAAC,gBAAgB,IAAI,oBAAoB,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC;AAED,kDAAkD;AAClD,SAAS,mBAAmB,CAAC,QAAsB;IACjD,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;AACrD,CAAC;AAED,8EAA8E;AAC9E,aAAa;AACb,KAAK,UAAU,eAAe,CAAC,OAI9B;IACC,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO,CAAC,IAAI,iBAAiB,CAAC,CAAC;IAC1D,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,QAAQ,EAAE;QAC7C,QAAQ,EAAE,QAAQ;KACnB,CAAC,CAAC;IACH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;QACjB,MAAM,KAAK,CACT,sBAAsB,OAAO,CAAC,IAAI,mBAAmB,QAAQ,CAAC,UAAU,EAAE,CAC3E,CAAC;IACJ,CAAC;IACD,MAAM,YAAY,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;IAE/D,OAAO,CAAC,GAAG,CAAC,aAAa,OAAO,CAAC,IAAI,qBAAqB,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC;IAC9E,MAAM,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;IAE5D,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;QACxD,CAAC,CAAC,MAAM;QACR,CAAC,CAAC,SAAS,CAAC;IACd,MAAM,eAAe,GACnB,OAAO,CAAC,OAAO,GAAG,GAAG,GAAG,OAAO,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC1D,MAAM,aAAE,CAAC,SAAS,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC;IAClD,IAAI,gBAAgB,KAAK,MAAM,EAAE,CAAC;QAChC,MAAM,UAAU,CAAC,eAAe,EAAE;YAChC,GAAG,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC;SAC5C,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,IAAA,aAAU,EAAC;YACT,IAAI,EAAE,eAAe;YACrB,GAAG,EAAE,OAAO,CAAC,OAAO;YACpB,IAAI,EAAE,IAAI;SACX,CAAC,CAAC;IACL,CAAC;IACD,MAAM,aAAE,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;AACnC,CAAC;AAED,KAAK,CAAC,KAAK,IAAI,EAAE;IACf,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,GAAG,CAAC,kBAAkB,CAAW,CAAC;QAClD,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC7B,MAAM,KAAK,CACT,8DAA8D,CAC/D,CAAC;QACJ,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CACvB,CACE,MAAM,aAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAC/D,CAAC,QAAQ,EAAE,CACoD,CAAC;QAEnE,IAAI,MAAM,CAAC,OAAO,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,KAAK,CACT,kEAAkE,CACnE,CAAC;QACJ,CAAC;QAED,MAAM,qBAAqB,GAAG,MAAM,CAAC,YAAY,EAAE,IAAI,CAAC;QACxD,IAAI,qBAAqB,KAAK,SAAS,EAAE,CAAC;YACxC,IAAI,qBAAqB,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC;gBAC1C,MAAM,KAAK,CACT,kFAAkF,CACnF,CAAC;YACJ,CAAC;YAED,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;QAC5C,MAAM,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;QACnD,MAAM,YAAY,GAAG,0BAA0B,CAAC,YAAY,CAAC,CAAC;QAC9D,MAAM,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;QAC9C,MAAM,MAAM,GAAG,YAAY,KAAK,YAAY,CAAC;QAC7C,MAAM,OAAO,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,WAAW;YACjB,QAAQ,EACN,sDAAsD;gBACtD,GAAG,OAAO,cAAc,OAAO,GAAG;gBAClC,GAAG,YAAY,IAAI,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACrD,GAAG,mBAAmB,CAAC,YAAY,CAAC,EAAE;YACxC,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrB,OAAO,CAAC,QAAQ,GAAG,CAAC,CAAC;IACvB,CAAC;AACH,CAAC,CAAC,EAAE,CAAC"}