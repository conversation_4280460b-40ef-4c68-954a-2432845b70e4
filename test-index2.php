<?php
/**
 * Very simple test index file - version 2
 */

echo "<h1>Basic PHP is working!</h1>";

// Try to load WordPress
$wp_load_path = __DIR__ . '/../../../wp-load.php';
if (file_exists($wp_load_path)) {
    echo "<p>Found wp-load.php at: " . $wp_load_path . "</p>";
    
    // Load WordPress
    require_once($wp_load_path);
    
    echo "<p>WordPress loaded successfully!</p>";
    
    // Check if we're in the theme context
    if (function_exists('get_bloginfo')) {
        echo "<p>Site name: " . get_bloginfo('name') . "</p>";
    }
    
    // Check if Timber is available
    if (class_exists('\\Timber\\Timber')) {
        echo "<p>Timber is available!</p>";
        
        // Try to create a basic Timber context
        try {
            $site = new \Timber\Site();
            echo "<p>Timber Site object created: " . $site->name . "</p>";
            
            // Try to get context
            $context = \Timber\Timber::context();
            echo "<p>Timber context created successfully!</p>";
            
        } catch (Exception $e) {
            echo "<p>Error with <PERSON><PERSON>: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>Timber is NOT available</p>";
    }
    
} else {
    echo "<p>Could not find wp-load.php at: " . $wp_load_path . "</p>";
}

echo "<p>End of test</p>";