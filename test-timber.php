<?php
/**
 * Test file to check if <PERSON><PERSON> is working
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if Tim<PERSON> is available
if (class_exists('<PERSON><PERSON>\Timber')) {
    echo "Timber is available!";
    
    // Try to get the site context
    $site = new Timber\Site();
    echo "<br>Site name: " . $site->name;
    
    // Try to get a menu
    $menu = Timber::get_menu('primary');
    if ($menu) {
        echo "<br>Primary menu found with " . count($menu->items) . " items";
    } else {
        echo "<br>No primary menu found";
    }
} else {
    echo "Timber is not available!";
}